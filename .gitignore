# Dependencies
node_modules/
.pnp/
.pnp.js
.pnpm-store/
.pnpm-debug.log*

# Build outputs
dist/
dist-ssr/
build/
*.local

# Data files (keep main db.json, exclude backups)
data/backup/
data/*.backup
data/*.bak
data/*.tmp

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
*.sublime-workspace
*.sublime-project

# Environment files
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# Testing
coverage/
.nyc_output/

# Cache directories
.npm/
.eslintcache
.stylelintcache
.prettiercache
.cache/

# Temporary files
*.tmp
*.temp
.temp/
temp/