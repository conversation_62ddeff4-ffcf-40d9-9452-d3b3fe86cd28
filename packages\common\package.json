{"name": "@webapp/common", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}}, "scripts": {"build": "tsc", "watch": "tsc -w", "clean": "rm -rf dist node_modules"}, "dependencies": {"ws": "^8.18.0"}, "devDependencies": {"@types/ws": "^8.5.10", "@types/node": "^20.4.5", "typescript": "^5.3.2"}}